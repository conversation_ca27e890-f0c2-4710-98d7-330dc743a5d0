/* 确保下拉选项不会超出容器 */
.ant-select-item-option-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保下拉菜单与输入框对齐 */
.job-title-select .ant-select-selector,
.status-select .ant-select-selector {
  border-radius: 8px !important;
}

/* 美化Modal标题样式 - 使用全局样式，不再单独定义 */
/* 增强标题区域 - 使用全局样式，不再单独定义 */

/* 基本信息和薪资信息标题样式 */
.section-title {
  text-align: center !important;
  font-weight: bold !important;
  margin-bottom: 16px !important;
}

/* 表单验证错误样式增强 */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input:focus,
.ant-form-item-has-error .ant-input-focused {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

.ant-form-item-explain-error {
  font-size: 12px !important;
  color: #ff4d4f !important;
  margin-top: 4px !important;
}

/* 必填字段标识 */
.ant-form-item-required::before {
  color: #ff4d4f !important;
  font-size: 14px !important;
}

/* 内容包装器样式 */
.coach-modal-content-wrapper {
  position: relative !important;
  min-height: 200px !important;
  overflow: hidden !important;
}

/* 加载蒙层样式 */
.coach-modal-loading-mask {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(255, 255, 255, 0.6) !important; /* 调整透明度为60% */
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 9999 !important;
  border-radius: 2px !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  height: 100% !important;
  cursor: not-allowed !important;
  user-select: none !important;
  pointer-events: all !important; /* 确保蒙层可以拦截点击事件 */
}

/* 加载提示文字样式 */
.coach-modal-loading-mask .ant-spin-text {
  margin-top: 12px !important;
  font-size: 16px !important;
  color: #1890ff !important;
  font-weight: bold !important;
  text-shadow: 0 0 2px white !important;
}

/* 加载图标样式 */
.coach-modal-loading-mask .ant-spin-dot {
  font-size: 36px !important;
}

/* 增强蒙板效果 */
.coach-modal-loading-mask::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: #fff !important;
  opacity: 0.6 !important; /* 调整透明度为60% */
  z-index: -1 !important;
}

/* 确保蒙层在模态框内容之上 */
.ant-modal-body {
  position: relative !important;
}

/* 当加载中时禁用所有表单元素 */
.coach-modal-loading-mask ~ form .ant-form-item-control-input,
.coach-modal-loading-mask ~ form .ant-select,
.coach-modal-loading-mask ~ form .ant-input,
.coach-modal-loading-mask ~ form .ant-radio-group,
.coach-modal-loading-mask ~ form .ant-picker,
.coach-modal-loading-mask ~ form .ant-btn {
  pointer-events: none !important;
  opacity: 0.7 !important;
}

/* 当加载中时禁用模态框按钮 */
.ant-modal-root .ant-modal-wrap[aria-modal="true"] .ant-modal-footer .ant-btn {
  pointer-events: auto !important;
}

/* 当加载中时禁用模态框关闭按钮 */
.coach-modal-loading-mask ~ .ant-modal-close {
  pointer-events: none !important;
  opacity: 0.5 !important;
}

/* 自定义 Spin 组件样式，使其与详情模态框一致 */
.ant-spin.ant-spin-spinning {
  max-height: none !important;
}

.ant-spin.ant-spin-spinning .ant-spin-dot {
  font-size: 24px !important;
}

.ant-spin-nested-loading > div > .ant-spin {
  max-height: none !important;
}

/* 确保蒙板的 z-index 不会覆盖标题 */
.ant-spin-nested-loading > div > .ant-spin .ant-spin-dot-spin {
  z-index: 1000 !important;
}

.ant-spin-container.ant-spin-blur::after {
  z-index: 999 !important;
  opacity: 0.6 !important;
}