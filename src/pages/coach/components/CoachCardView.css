/* 通用卡片样式 */
.coach-card-horizontal {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.coach-card-horizontal:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 横向布局的内容区域 */
.coach-card-content-horizontal {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
  position: relative;
  height: 100%;
}

/* 头像容器 - 左侧 */
.coach-avatar-container-horizontal {
  margin-right: 10px;
  position: relative;
  flex-shrink: 0;
}

.coach-avatar-wrapper {
  position: relative;
  display: inline-block;
}

/* 状态标识 - 头像右上角 */
.coach-status-badge-horizontal {
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 1;
  transform: scale(0.9);
}

/* 教练信息容器 - 中间部分 */
.coach-info-container-horizontal {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.coach-info-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 2px;
}

/* 名字和职位行 */
.coach-name-row-horizontal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  flex-wrap: wrap;
}

.coach-name {
  font-size: 15px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.coach-gender {
  font-size: 13px;
  font-weight: normal;
  margin-left: 3px;
  flex-shrink: 0;
}

.coach-age {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
  font-weight: normal;
  flex-shrink: 0;
}

.coach-job-title-horizontal {
  display: flex;
  flex-wrap: wrap;
  margin-top: 4px;
}

.coach-id {
  font-size: 12px;
  color: #888;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 教练详细信息部分 */
.coach-info-details {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  color: #666;
  gap: 4px;
}

.coach-info-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 4px;
}

.coach-phone, .coach-experience, .coach-hire-date {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.coach-hire-date {
  color: #555;
  margin-top: 2px;
}

/* 操作按钮容器 - 右侧 */
.coach-actions-container {
  margin-left: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 操作按钮 */
.edit-button, .delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

.edit-button:hover {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.delete-button:hover {
  color: #f5222d;
  background-color: rgba(245, 34, 45, 0.1);
}

/* ----- 全新高档样式 ----- */
.coach-card-premium {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  background: white;
  height: 100%;
  border: none;
  width: 100%;
  min-width: 280px;
  display: flex;
  flex-direction: column;
}

.coach-card-premium:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

/* 背景装饰 */
.card-background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 75px;
  background: linear-gradient(135deg, rgba(26, 41, 128, 0.7) 0%, #26d0ce 100%);
  z-index: 0;
  overflow: hidden;
}

.card-decoration-circle {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  right: -40px;
  top: -50px;
  background: rgba(255, 255, 255, 0.15);
}

.card-decoration-circle::before {
  content: '';
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  left: -12px;
  bottom: 12px;
  background: rgba(255, 255, 255, 0.15);
}

/* 卡片内容 */
.coach-card-content-premium {
  position: relative;
  z-index: 1;
  padding: 20px 16px 6px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  height: 100%;
  width: 100%;
}

/* 头部区域 - 头像和姓名 */
.coach-header-section {
  display: flex;
  gap: 8px;
  margin-bottom: 0;
  align-items: flex-start;
}

.coach-avatar-container-premium {
  position: relative;
  margin-top: 0;
  flex-shrink: 0;
}

.premium-avatar {
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.coach-gender-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  z-index: 2;
  transform: scale(0.9);
}

.gender-icon-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.gender-icon {
  font-size: 12px;
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: bold;
}

.gender-icon.male {
  background-color: #1890ff;
  color: white;
}

.gender-icon.female {
  background-color: #eb2f96;
  color: white;
}

/* 标题区域 */
.coach-title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 0;
  min-width: 0;
  padding-top: 1px;
}

.coach-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.coach-name-premium {
  font-size: 16px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  margin-right: 6px;
}

.coach-job-title-premium {
  display: flex;
  margin-top: 1px;
}

.coach-status-age-row {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.coach-age-premium {
  font-size: 11px;
  color: #444;
  padding: 2px 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* 职位标签包装器 */
.coach-job-title-wrapper {
  display: flex;
  margin-left: 68px;
  margin-top: -26px;
  margin-bottom: 14px;
  justify-content: flex-end;
  align-items: center;
  width: calc(100% - 68px);
  position: relative;
}

/* 状态标签 */
.coach-status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  top: -5px;
  right: 6px;
}

/* 状态下拉菜单样式 */
.ant-dropdown {
  z-index: 1050 !important;
}

/* 年龄包装器 */
.coach-age-wrapper {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 0;
}

/* 操作按钮 */
.coach-actions-premium {
  display: flex;
  justify-content: center;
  gap: 8px;
  position: absolute;
  top: -10px;
  right: 8px;
  z-index: 5;
  background-color: rgba(255,255,255,0.6);
  border-radius: 6px;
  padding: 2px 5px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

.action-button {
  transition: all 0.2s ease;
  opacity: 1;
  border: none;
  background-color: transparent !important;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  margin: 0;
}

.action-button:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.03) !important;
}

.action-button.edit {
  color: #1890ff !important;
  border: none;
}

.action-button.delete {
  color: #f5222d !important;
  border: none;
}

/* 信息内容 */
.coach-info-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  background-color: #f9f9f9;
  padding: 10px 12px;
  border-radius: 6px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
  margin-top: 0;
  margin-bottom: 6px;
  width: 100%;
}

/* 列表项样式 */
.ant-list-item {
  width: 100%;
  display: flex;
}

.ant-list-grid .ant-list-item {
  margin-bottom: 0;
}

.ant-card {
  width: 100%;
}

.coach-info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
  margin-bottom: 2px;
}

.info-icon {
  color: #1890ff;
  font-size: 12px;
  width: 16px;
  flex-shrink: 0;
}

.info-label {
  color: #666;
  font-weight: 500;
  font-size: 12px;
  width: 42px;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  font-weight: 500;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  padding-left: 2px;
}

/* 旧样式兼容 */
.coach-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.coach-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.coach-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.coach-avatar-container {
  margin-bottom: 16px;
  position: relative;
}

.coach-status-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  z-index: 1;
}

.coach-info-container {
  width: 100%;
  text-align: center;
}

.coach-name-row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 4px;
}

.coach-job-title {
  display: flex;
  justify-content: center;
  margin-bottom: 6px;
}

/* 响应式样式调整 */
@media (max-width: 1400px) {
  .coach-card-premium {
    height: auto;
    min-width: 260px;
  }

  .coach-info-content {
    margin-top: 6px;
  }
}

@media (max-width: 992px) {
  .coach-card-premium {
    min-width: 240px;
  }
}

@media (max-width: 768px) {
  .coach-header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .coach-avatar-container-premium {
    align-self: center;
    margin-bottom: 6px;
  }

  .coach-card-premium {
    min-width: 100%;
  }
}

/* 职位标签显示 */
.coach-job-title-display {
  display: flex;
  margin-top: -8px;
  margin-bottom: 4px;
  margin-left: 2px;
}

/* 证书显示相关样式 */
.coach-certifications {
  margin-top: 6px;
  align-items: center !important;
  display: flex !important;
  flex-direction: row !important;
}

.certifications-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 0;
  align-items: center;
}

.cert-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.cert-tag {
  margin-right: 0;
  font-size: 11px !important;
  padding: 0 10px !important;
  height: 22px !important;
  line-height: 22px !important;
  border-radius: 4px !important;
  margin-bottom: 0;
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  border: none !important;
  display: inline-flex;
  align-items: center;
}

.no-cert {
  color: #999;
  font-size: 12px;
  font-style: italic;
}