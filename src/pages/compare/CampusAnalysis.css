/* 校区分析页面样式 - 采用数据统计页面风格 */
.campus-analysis {
  padding: 0 4px;
}

.campus-analysis-card {
  margin-top: -12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
}

.campus-analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0;
}

.campus-analysis-title {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600;
  color: #262626;
}

.campus-analysis-actions {
  display: flex;
  align-items: center;
}

/* 概览区域样式 */
.campus-overview-section {
  background-color: #fff;
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

.section-header {
  padding: 20px 24px 0;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
}

.section-icon {
  margin-right: 8px;
  color: #1890ff;
}

.overview-content {
  padding: 0 24px 24px;
}

/* 图表布局样式 */
.campus-charts-layout {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.campus-charts-row {
  display: flex;
  gap: 24px;
}

.chart-container-card {
  background-color: #fff;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  flex: 1;
  min-height: 450px;
}

.chart-container-card.full-width {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
}

.chart-icon {
  margin-right: 8px;
  color: #1890ff;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.chart-content {
  height: 350px;
  width: 100%;
}

/* Radio按钮样式优化 */
.chart-controls .ant-radio-group {
  border-radius: 6px;
}

.chart-controls .ant-radio-button-wrapper {
  font-weight: normal;
  border-radius: 0;
}

.chart-controls .ant-radio-button-wrapper:first-child {
  border-radius: 6px 0 0 6px;
}

.chart-controls .ant-radio-button-wrapper:last-child {
  border-radius: 0 6px 6px 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .campus-charts-row {
    flex-direction: column;
  }

  .chart-container-card {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .campus-analysis {
    padding: 0;
  }

  .campus-analysis-card {
    margin-top: -8px;
    border-radius: 8px;
  }

  .campus-analysis-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .chart-container-card {
    padding: 16px;
    min-height: 350px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart-content {
    height: 300px;
  }
}