/* 缴费记录页面样式 */
.payment-management {
  padding: 0 4px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.payment-management-card {
  margin-top: -12px;
  border-radius: 8px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0;
}



/* 搜索栏样式 - 已在payment.css中定义 */

/* 响应式布局 */
@media (max-width: 1200px) {
  .table-toolbar .ant-row {
    flex-wrap: wrap !important;
  }
  
  .table-toolbar .ant-col {
    min-width: auto !important;
    margin-bottom: 8px;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .payment-management {
    padding: 0;
  }

  .payment-management-card {
    margin-top: -8px;
  }
} 