/* 缴费记录页面的Select组件样式 */

/* 修复Select组件和其他表单元素的样式 */
.ant-select {
  width: 100%;
}

.ant-select-selector {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  padding: 0 11px !important;
}

.payment-records-container {
  padding: 24px;
}

.payment-statistics {
  margin-bottom: 24px;
}

.payment-filter-bar {
  margin-bottom: 16px;
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.payment-filter-buttons {
  margin-top: 24px;
  text-align: right;
}

.status-badge {
  margin-right: 0;
}

.payment-modal-divider {
  margin: 0 0 24px 0;
}

/* 解决模态框中分隔线重复的问题 */
.payment-receipt-modal .ant-modal-body {
  padding-top: 0;
}

.payment-receipt-modal .ant-divider {
  margin-top: 0;
}

.payment-receipt-descriptions {
  margin-top: 0;
}

/* 给删除确认模态框和编辑模态框添加相同的样式，解决分隔线重复问题 */
.ant-modal .ant-modal-body {
  padding-top: 0;
}

.ant-modal .ant-divider {
  margin-top: 0;
}

.payment-dropdown {
  min-width: 200px !important;
}

/* 统一的间距类 - 与打卡消课页面保持一致 */
.mb-0 {
  margin-bottom: 0 !important;
}

.mb-6 {
  margin-bottom: 24px !important;
}

/* 优化过滤栏样式 */
.table-toolbar {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

/* 搜索栏响应式样式 */
.table-toolbar .ant-input-search {
  border-radius: 4px;
}

.table-toolbar .ant-select {
  border-radius: 4px;
}

.table-toolbar .ant-picker {
  border-radius: 4px;
}

.table-toolbar .ant-picker-input input {
  text-align: center;
}

.table-toolbar .ant-picker-range .ant-picker-input input {
  text-align: center;
}

/* 按钮样式优化 */
.table-toolbar .ant-btn {
  border-radius: 4px;
  font-weight: 500;
}

.table-toolbar .ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.table-toolbar .ant-btn-primary:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

/* 表单项样式统一 */
.table-toolbar .ant-form-item {
  margin-bottom: 0;
}

.table-toolbar .ant-form-item-control {
  line-height: 32px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .table-toolbar {
    padding: 12px;
  }
  
  .table-toolbar .ant-btn {
    margin-right: 4px !important;
    margin-bottom: 8px;
  }
  
  .table-toolbar .ant-btn:last-child {
    margin-right: 0 !important;
  }
}

/* 小屏幕时按钮间距调整 */
@media (max-width: 576px) {
  .table-toolbar .ant-btn {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }
  
  .table-toolbar .ant-btn:last-child {
    margin-bottom: 0;
  }
}

/* 确保表单项间距一致 */
.table-toolbar .ant-row {
  width: 100%;
}

/* 优化下拉菜单宽度 */
.table-toolbar .ant-select-dropdown {
  min-width: fit-content !important;
}