/* 课表视图整体容器 */
.schedule-view {
  padding: 0 4px;
}

.schedule-management-card {
  margin-top: -12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.schedule-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin: 16px 0;
}

/* 图例样式 - 移除边框和标题 */
.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  margin-bottom: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.legend-item:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.legend-item.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.legend-item.active:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.legend-item span {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
}

.legend-item.active span {
  color: white;
  font-weight: 600;
}

/* 课表容器 */
.schedule-container {
  overflow-x: auto;
  border: 1px solid #e6f7ff;
  border-radius: 8px;
  background-color: #fafafa;
}

/* 课表网格 */
.schedule {
  display: grid;
  grid-template-columns: 120px repeat(7, 1fr);
  min-width: 1000px;
  background-color: white;
  border-radius: 6px;
  overflow: hidden;
}

.schedule-header {
  background-color: #f0f8ff;
  border: 1px solid #d6f3ff;
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  color: #1890ff;
}

.schedule-time {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 12px 8px;
  text-align: center;
  font-weight: 500;
  font-size: 13px;
  color: #495057;
  display: flex;
  align-items: center;
  justify-content: center;
}

.schedule-cell {
  border: 1px solid #e9ecef;
  padding: 8px;
  min-height: 80px;
  background-color: white;
  display: flex;
  flex-direction: column;
  gap: 6px;
  transition: background-color 0.2s ease;
}

.schedule-cell:hover {
  background-color: #f8f9fa;
}

/* 学生/课程信息样式 */
.student-info {
  background-color: #fff !important;
  border: 1px solid #e9ecef !important;
  border-left: 3px solid #1890ff !important;
  border-radius: 4px !important;
  padding: 8px !important;
  margin-bottom: 4px !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.student-info:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
  background-color: #f0f9ff !important;
}

.student-info .name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  font-size: 13px;
}

.student-info .details {
  color: #7f8c8d;
  font-size: 11px;
  line-height: 1.3;
  margin-bottom: 2px;
}

.student-info .course-info {
  color: #3498db;
  font-weight: 500;
  margin-top: 2px;
}

.student-info .description {
  color: #95a5a6;
  font-style: italic;
  margin-top: 2px;
}

/* 空状态样式 */
.schedule-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #95a5a6;
  font-size: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-view {
    padding: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #e8f2ff 100%);
  }

  .schedule-card {
    margin-top: -8px;
    background: rgba(255, 255, 255, 0.9);
  }
  
  .schedule-content {
    background: rgba(248, 250, 252, 0.5);
    padding: 12px;
  }
  
  .schedule {
    grid-template-columns: 100px repeat(7, 120px);
  }
  
  .schedule-header,
  .schedule-time {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .schedule-cell {
    padding: 6px;
    min-height: 60px;
  }
  
  .student-info {
    padding: 6px;
    font-size: 11px;
  }
  
  .student-info .name {
    font-size: 12px;
  }
  
  .student-info .details {
    font-size: 10px;
  }
}

.page-container {
  padding: 0 4px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f2ff 100%);
}

.schedule-view-container {
  padding: 0;
  background: transparent;
}

.schedule-card {
  margin-top: -12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.schedule-content {
  padding: 20px 0 0 0;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 8px;
  margin: 16px;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.page-title {
  font-size: 18px;
  font-weight: 600;
}

.filter-and-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  width: 100%;
}

.schedule-controls {
  width: 100%;
  margin-right: 16px;
  flex: 1;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 16px;
}

.schedule-cell.draggable .student-info {
  cursor: grab;
}

.schedule-cell.draggable .student-info:active {
  cursor: grabbing;
}

.schedule-cell.draggable .student-group {
  cursor: grab;
}

.schedule-cell.draggable .student-group:active {
  cursor: grabbing;
}

.schedule-cell.drop-target {
  background-color: rgba(52, 152, 219, 0.1);
  border: 2px dashed #3498db;
}

.student-group {
  display: block;
  width: 100%;
  margin: 4px 0;
  border: 2px solid;
  border-radius: 5px;
  overflow: hidden;
}

.student-group.coach-li { border-color: rgba(231, 76, 60, 0.4); }
.student-group.coach-wang { border-color: rgba(52, 152, 219, 0.4); }
.student-group.coach-zhang { border-color: rgba(46, 204, 113, 0.4); }
.student-group.coach-liu { border-color: rgba(243, 156, 18, 0.4); }

.student-group .student-info {
  margin: 0;
  border-radius: 0;
}

.student-group .student-info.group-first {
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.student-group .student-info.group-middle {
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.student-info .name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.student-info .course-info {
  margin-top: 2px;
  font-style: italic;
}

.change-item {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.change-header {
  font-weight: 600;
  margin-bottom: 10px;
  color: #3498db;
}

.change-detail {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.change-before, .change-after {
  flex: 1;
}

.change-label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #34495e;
}

.student-change {
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 5px;
}

/* 教练背景色样式 - 动态生成的类名 */
.student-info.coach-1 { 
  background-color: #ffe6e6 !important; 
}

.student-info.coach-2 { 
  background-color: #e6f4ff !important; 
}

.student-info.coach-3 { 
  background-color: #e6ffe6 !important; 
}

.student-info.coach-4 { 
  background-color: #fff7e6 !important; 
}

.student-info.coach-5 { 
  background-color: #f6e6ff !important; 
}

.student-info.coach-6 { 
  background-color: #e6fff9 !important; 
}

.student-info.coach-7 { 
  background-color: #fffce6 !important; 
}

.student-info.coach-8 { 
  background-color: #f0f0f0 !important; 
}

/* 样式优化已完成 */ 