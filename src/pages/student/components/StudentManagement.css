/* 学员管理页面样式 */
.student-management {
  padding: 0 4px;
}

.student-management-card {
  margin-top: -12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.student-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.student-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.student-actions {
  display: flex;
  align-items: center;
}

.add-student-button {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
  padding: 0 16px;
  height: 32px;
}

.add-student-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .student-management {
    padding: 0;
  }

  .student-management-card {
    margin-top: -8px;
  }
}
