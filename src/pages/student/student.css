.student-list-container {
  padding: 24px;
}

.student-search-bar {
  margin-bottom: 16px;
  padding: 16px 0;
}

.student-search-bar .ant-form-item {
  margin-bottom: 12px;
}

.student-table-container {
  margin-top: 16px;
  width: 100%;
  overflow-x: auto; /* 允许容器水平滚动 */
  /* 确保表格布局合理 */
  table-layout: fixed;
}

/* 确保表格单元格内容不换行 */
.student-table-container .ant-table-cell {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 调整课程信息单元格的内边距，消除顶部间距 */
.student-table-container .course-info-column.ant-table-cell {
  padding-top: 4px !important; /* 保留适当的顶部内边距 */
  padding-bottom: 4px !important; /* 保留适当的底部内边距 */
  vertical-align: middle !important;
}

/* 确保表格行不换行 */
.student-table-row {
  white-space: nowrap !important;
}

/* 确保表格头部不换行 */
.student-table-container .ant-table-thead th {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 确保表格内容区域可以水平滚动 */
.student-table-container .ant-table-body {
  overflow-x: auto !important;
}

.student-form-item {
  margin-bottom: 16px;
}

.course-group-table {
  margin-top: 16px;
  margin-bottom: 16px;
}

.course-group-form {
  padding: 16px;
  background-color: #f8f8f8;
  border-radius: 4px;
  margin-bottom: 16px;
}

.course-schedule-list {
  margin-top: 8px;
}

.course-schedule-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.course-schedule-item .day {
  width: 80px;
  margin-right: 12px;
}

.course-schedule-item .time {
  flex: 1;
}

.course-schedule-item .actions {
  width: 60px;
  text-align: right;
}

.student-form-section {
  margin-bottom: 24px;
}

.student-form-section-title {
  margin-bottom: 16px;
  font-weight: 500;
  font-size: 16px;
}

.student-action-buttons {
  display: flex;
  justify-content: space-between; /* 均匀分布 */
  align-items: center;
  width: 100%;
  min-width: 135px; /* 增加最小宽度 */
  padding: 0 5px; /* 添加内边距 */
}

/* 打卡模态框样式 */
.attendance-form .ant-form-item {
  margin-bottom: 16px;
}

.attendance-form .ant-form-item-label {
  padding-bottom: 4px;
}

.attendance-form .ant-input[disabled] {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.65);
}

/* 打卡模态框标题分隔线 - 使用全局样式，不再单独定义 */

/* 表格样式 */
.student-table-container .ant-table-thead > tr > th {
  text-align: center;
  background-color: #fafafa;
}

.student-table-container .ant-table-tbody > tr > td {
  text-align: center;
}

/* 固定操作列样式 - 全局应用 */
.student-table-container .ant-table-thead > tr > th.ant-table-cell-fix-right,
.student-table-container .ant-table-tbody > tr > td.ant-table-cell-fix-right {
  position: sticky !important;
  right: 0 !important;
  z-index: 2 !important;
}

/* 确保表头固定列背景色正确 */
.student-table-container .ant-table-thead > tr > th.ant-table-cell-fix-right {
  background-color: #f5f5f5 !important; /* 设置为与其他表头一致的背景色 */
}

/* 确保表格内容固定列背景色正确 */
.student-table-container .ant-table-tbody > tr > td.ant-table-cell-fix-right {
  background-color: transparent !important; /* 改为透明背景 */
  transition: background-color 0.3s ease; /* 添加过渡效果 */
}

/* 固定列hover效果 */
.student-table-container .ant-table-tbody > tr > td.ant-table-cell-fix-right:hover {
  background-color: #f5f5f5 !important; /* hover时显示灰色背景 */
}

/* 确保斑马纹行的固定列背景色正确 */
.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(even) > td.ant-table-cell-fix-right {
  background-color: transparent !important; /* 改为透明背景 */
}

.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td.ant-table-cell-fix-right {
  background-color: transparent !important; /* 改为透明背景 */
}

/* 斑马纹行的固定列hover效果 */
.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(even) > td.ant-table-cell-fix-right:hover,
.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td.ant-table-cell-fix-right:hover {
  background-color: #f5f5f5 !important; /* hover时显示灰色背景 */
}

/* 确保操作列按钮容器有足够空间 */
.student-table-container .ant-table-tbody > tr > td.operation-column {
  min-width: 140px !important; /* 增加最小宽度 */
  background-color: transparent !important; /* 移除默认背景色 */
  transition: background-color 0.3s ease; /* 添加过渡效果 */
}

/* 操作列hover效果 - 响应式 */
.student-table-container .ant-table-tbody > tr > td:last-child:hover,
.student-table-container .ant-table-tbody > tr > td.operation-column:hover,
.student-table-container .ant-table-tbody > tr > td.ant-table-cell-fix-right:hover {
  background-color: #f5f5f5 !important; /* hover时显示灰色背景 */
}

/* 确保奇偶行操作列的hover效果 */
.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td.operation-column:hover,
.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(even) > td.operation-column:hover,
.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td.ant-table-cell-fix-right.operation-column:hover,
.student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(even) > td.ant-table-cell-fix-right.operation-column:hover {
  background-color: #f5f5f5 !important; /* hover时显示灰色背景 */
}

.student-table-container .ant-btn-link {
  color: #1890ff;
}

.student-table-container .ant-btn-link:hover {
  color: #40a9ff;
}

/* 滚动提示样式 - 默认隐藏 */
.student-table-container .scroll-indicator {
  display: none;
}

/* 居中表格样式 */
.centered-table .ant-table-thead > tr > th {
  text-align: center !important;
  background-color: #fafafa;
}

.centered-table .ant-table-tbody > tr > td {
  text-align: center !important;
}

/* 固定宽度标签样式 */
.fixed-width-tag {
  width: 60px !important;
  text-align: center !important;
  display: inline-block !important;
}

/* 课程类型标签容器 */
.course-type-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 全局标签样式覆盖 */
.student-table-container .ant-tag {
  min-width: 70px;
  text-align: center;
  font-weight: 500;
  padding: 4px 0;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

/* 标签悬停效果 */
.student-table-container .ant-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 课程记录模态框中，用于分隔不同课程组的样式 (★ 删除) */
/*
.course-group-separator td {
  border-top: 2px dashed #e8e8e8;
}
*/

/* 课程信息列滚动容器 */
.course-info-scroll-container {
  width: fit-content !important; /* 改为自适应内容宽度 */
  overflow-x: auto;
  display: flex !important;
  flex-direction: column !important;
  gap: 8px !important; /* 课程块之间的间距改为8px，与课程块和表格边框的距离一致 */
  padding: 8px 0 !important; /* 调整为与表格单元格一致的上下内边距 */
  position: relative; /* 为固定定位的子元素提供参考 */
}

/* 课程信息列样式 */
.course-info-column {
  overflow-x: auto !important;
  position: relative; /* 为固定定位的子元素提供参考 */
  padding: 0; /* 重置内边距 */
}

/* 课程信息网格布局 */
.course-info-grid {
  display: grid;
  grid-template-columns: 4px 110px 80px 80px 85px 80px 1fr !important; /* 重新设计列宽：竖线+课程名称+课程类型+教练+课时+状态+操作按钮 */
  column-gap: 16px !important; /* 统一16px间距 */
  align-items: center;
  width: 100% !important;
  min-width: 100% !important;
  padding: 4px 8px !important;
  border-radius: 4px;
  box-sizing: border-box !important;
}

/* 课程名称列左对齐样式 */
.course-info-grid > div:nth-child(2) {
  text-align: left !important;
  padding-left: 8px !important; /* 增加左边距，与竖线保持合适距离 */
}

/* 课程类型列居中 */
.course-info-grid > div:nth-child(3) {
  text-align: center !important;
}

/* 教练列居中 */
.course-info-grid > div:nth-child(4) {
  text-align: center !important;
}

/* 课时列居中 */
.course-info-grid > div:nth-child(5) {
  text-align: center !important;
}

/* 状态列居中 */
.course-info-grid > div:nth-child(6) {
  text-align: center !important;
}

/* 操作按钮列居中 */
.course-info-grid > div:nth-child(7) {
  text-align: center !important;
}

/* 操作列样式 */
.operation-column {
  z-index: 10 !important;
  position: sticky !important;
  right: 0 !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
  /* 移除背景色设置，使用全局设置 */
}

/* 响应式表格样式 - 适配小屏幕 */
@media screen and (max-width: 1200px) {
  /* 确保表格可以水平滚动 */
  .student-table-container .ant-table {
    width: 100%;
    overflow-x: auto;
  }

  /* 确保课程信息单元格在小屏幕上也没有顶部内边距 */
  .student-table-container .course-info-column.ant-table-cell {
    padding-top: 4px !important; /* 保留适当的顶部内边距 */
    padding-bottom: 4px !important; /* 保留适当的底部内边距 */
    vertical-align: middle !important;
  }

  /* 确保操作列在小屏幕上正确显示 */
  .operation-column {
    position: sticky !important;
    right: 0 !important;
    z-index: 20 !important;
    /* 背景色由全局样式设置 */
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05) !important; /* 轻微阴影效果 */
  }

  /* 确保操作列按钮在小屏幕上正确显示 */
  .student-table-container .ant-table-tbody > tr > td:last-child,
  .student-table-container .ant-table-thead > tr > th:last-child {
    white-space: nowrap;
    min-width: 140px; /* 增加最小宽度 */
    position: sticky;
    right: 0;
    z-index: 2;
    background-color: transparent !important; /* 移除默认背景色 */
    transition: background-color 0.3s ease; /* 添加过渡效果 */
  }

  /* 操作列hover效果 - 响应式 */
  .student-table-container .ant-table-tbody > tr > td:last-child:hover,
  .student-table-container .ant-table-tbody > tr > td.operation-column:hover,
  .student-table-container .ant-table-tbody > tr > td.ant-table-cell-fix-right:hover {
    background-color: #f5f5f5 !important; /* hover时显示灰色背景 */
  }

  /* 确保操作列背景色与行背景色一致 - 移除这些设置，使用透明背景 */
  .student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td:last-child {
    background-color: transparent !important; /* 改为透明背景 */
  }

  .student-table-container .ant-table-tbody > tr.ant-table-row:nth-child(even) > td:last-child {
    background-color: transparent !important; /* 改为透明背景 */
  }

  /* 确保课程信息列在小屏幕上可以水平滚动 */
  .student-table-container .course-info-column {
    overflow-x: auto !important;
  }

  /* 课程信息内容容器样式 */
  .student-table-container .course-info-column > div {
    max-width: 100%;
    overflow-x: auto;
    padding-bottom: 5px; /* 添加底部间距，确保滚动条不会被遮挡 */
  }

  /* 确保表格内容可以水平滚动 */
  .student-table-container .ant-table-body {
    overflow-x: auto !important;
  }

  /* 确保操作按钮在小屏幕上不会换行 */
  .student-action-buttons {
    min-width: 135px;
    gap: 0; /* 移除间距，使用外边距控制 */
  }

  /* 课程信息网格容器样式 - 确保可以水平滚动 */
  .student-table-container .course-info-grid {
    min-width: 600px; /* 调整最小宽度适应新布局 */
    width: 100% !important;
    display: grid;
    grid-template-columns: 4px 110px 80px 80px 85px 80px 1fr !important; /* 与基础样式保持一致 */
    column-gap: 16px !important; /* 与基础样式保持一致 */
    position: relative;
    margin-right: 0;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 4px 8px !important;
  }

  /* 添加滚动提示样式 */
  .student-table-container .scroll-indicator {
    display: block;
    text-align: center;
    color: #1890ff;
    font-size: 12px;
    margin-top: 2px;
    opacity: 0.7;
  }

  /* 缩小按钮在小屏幕下的间距 */
  .action-button {
    margin: 0 1px !important; /* 减小外边距 */
  }
}

/* 带单位的输入框/选择框样式 */
.input-with-unit-wrapper {
  position: relative;
  width: 100%;
}

.input-with-unit-wrapper .select-with-unit .ant-select-selector,
.input-with-unit-wrapper .select-with-unit.ant-input-number {
  padding-right: 45px !important;
  width: 100% !important;
}

.input-with-unit-wrapper .input-unit {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  background-color: #f5f5f5;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-left: 1px solid #d9d9d9;
  pointer-events: none;
  z-index: 1;
}

/* 修复下拉框样式 */
.ant-select {
  width: 100% !important;
}

.ant-select-selector {
  width: 100% !important;
  box-sizing: border-box !important;
}

.ant-select-dropdown {
  min-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 确保下拉选项文本不换行 */
.ant-select-item-option-content {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 垂直布局的操作按钮容器 */
.student-action-buttons-vertical {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  background-color: transparent !important;
}

/* 垂直布局按钮的悬停效果 */
.student-action-buttons-vertical .ant-btn {
  transition: all 0.3s;
  width: 32px;
  height: 32px;
}

.student-action-buttons-vertical .ant-btn:hover {
  transform: scale(1.2);
}

/* 单个操作按钮样式 */
.action-button {
  padding: 0 !important;
  width: 32px !important; /* 增加按钮宽度 */
  height: 32px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 50% !important;
  transition: all 0.3s;
  background-color: transparent !important; /* 确保初始状态透明 */
  margin: 0 2px !important; /* 添加左右外边距 */
}

.action-button:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.05) !important; /* 悬停时略微加深 */
}

/* 表头样式统一 */
.student-table-container .ant-table-thead > tr > th {
  background-color: #f5f5f5 !important;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.3s ease;
  padding: 16px 8px;
  text-align: center;
}

/* 最高优先级操作列hover效果 - 确保生效 */
.student-table-container .ant-table-tbody > tr:hover > td.operation-column,
.student-table-container .ant-table-tbody > tr:hover > td.ant-table-cell-fix-right.operation-column,
.student-table-container .ant-table-tbody > tr:hover > td:last-child,
.student-table-container .ant-table-tbody > tr:hover > td.ant-table-cell-fix-right:last-child {
  background-color: #f5f5f5 !important;
}

/* 统一所有单元格的hover背景色 */
.student-table-container .ant-table-tbody > tr:hover > td,
.student-table-container .ant-table-tbody > tr.ant-table-row-hover > td,
.student-table-container .ant-table-tbody > tr:hover > td.ant-table-cell-fix-right,
.student-table-container .ant-table-tbody > tr.ant-table-row-hover > td.ant-table-cell-fix-right {
  background-color: #f5f5f5 !important;
}