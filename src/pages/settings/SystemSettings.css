/* 系统设置页面样式 */
.settings-management {
  padding: 0 4px;
}

.settings-management-card {
  margin-top: -12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0;
  border-bottom: none;
}

.settings-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  flex: 1;
}

.settings-content {
  margin-top: 20px;
}

/* 标签页样式 - 参考统计页面设计 */
.settings-management .header-tabs {
  flex: 1;
  margin-left: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.settings-management .header-tabs .ant-tabs {
  width: auto;
}

.settings-management .header-tabs .ant-tabs-nav {
  margin-bottom: 0;
  width: auto;
  display: flex;
  justify-content: flex-end;
}

.settings-management .header-tabs .ant-tabs-nav-wrap {
  display: flex;
  justify-content: flex-end;
}

.settings-management .header-tabs .ant-tabs-nav-list {
  display: flex;
  justify-content: flex-end;
}

.settings-management .header-tabs .ant-tabs-tab {
  padding: 8px 16px;
  font-size: 15px;
  margin: 0 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: #666;
  font-weight: 500;
}

.settings-management .header-tabs .ant-tabs-tab:hover {
  color: #1677ff;
  background-color: rgba(22, 119, 255, 0.06);
}

.settings-management .header-tabs .ant-tabs-tab-active {
  color: #1677ff !important;
  background-color: rgba(22, 119, 255, 0.1);
  font-weight: 600;
}

.settings-management .header-tabs .ant-tabs-ink-bar {
  display: none;
}

/* 不同标签页的颜色主题 */
.settings-management .header-tabs .ant-tabs-tab:nth-child(1).ant-tabs-tab-active {
  color: #1677ff !important;
  background-color: rgba(22, 119, 255, 0.1) !important;
  border-bottom: 2px solid #1677ff;
}

.settings-management .header-tabs .ant-tabs-tab:nth-child(2).ant-tabs-tab-active {
  color: #52c41a !important;
  background-color: rgba(82, 196, 26, 0.1) !important;
  border-bottom: 2px solid #52c41a;
}

.settings-management .header-tabs .ant-tabs-tab:nth-child(3).ant-tabs-tab-active {
  color: #722ed1 !important;
  background-color: rgba(114, 46, 209, 0.1) !important;
  border-bottom: 2px solid #722ed1;
}

.settings-management .header-tabs .ant-tabs-tab:nth-child(4).ant-tabs-tab-active {
  color: #fa8c16 !important;
  background-color: rgba(250, 140, 22, 0.1) !important;
  border-bottom: 2px solid #fa8c16;
}

/* 标签页图标和文字样式 */
.settings-management .tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* 内容区域样式 */
.settings-management .ant-tabs-content {
  padding: 20px 0;
  background-color: #fff;
}

.settings-management .ant-tabs-tabpane {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单和组件样式优化 */
.settings-management .ant-form-item-label > label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.settings-management .ant-divider {
  margin: 20px 0 24px;
  border-color: #f0f0f0;
}

.settings-management .ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

/* 选项管理样式 - 优先级更高的样式 */
.settings-management .options-tab {
  background: #fff;
  border-radius: 8px;
  height: 100%;
}

.settings-management .settings-vertical-tabs {
  height: 100% !important;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.settings-management .settings-vertical-tabs .ant-tabs-nav {
  background: #fafafa !important;
  border-radius: 8px 0 0 8px !important;
  padding: 12px 8px !important;
  margin-right: 0 !important;
  width: 200px !important;
  border-right: 1px solid #f0f0f0 !important;
}

.settings-management .settings-vertical-tabs .ant-tabs-tab {
  padding: 12px 16px !important;
  margin: 4px 0 !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
  color: #666 !important;
  font-weight: 500 !important;
  background: transparent !important;
  border: none !important;
}

.settings-management .settings-vertical-tabs .ant-tabs-tab:hover {
  background-color: rgba(22, 119, 255, 0.06) !important;
  color: #1677ff !important;
}

.settings-management .settings-vertical-tabs .ant-tabs-tab-active {
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.1) 0%, rgba(22, 119, 255, 0.15) 100%) !important;
  color: #1677ff !important;
  font-weight: 600 !important;
  position: relative;
}

.settings-management .settings-vertical-tabs .ant-tabs-tab-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: #1677ff;
  border-radius: 0 2px 2px 0;
}

.settings-management .settings-vertical-tabs .ant-tabs-ink-bar {
  display: none !important;
}

.settings-management .settings-vertical-tabs .ant-tabs-content {
  padding: 20px !important;
  background: #fff;
}

.settings-management .option-list {
  max-height: 350px;
  overflow-y: auto;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  background: #fff;
}

.settings-management .option-item {
  transition: all 0.3s ease;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.settings-management .option-item:last-child {
  border-bottom: none;
}

.settings-management .option-item:hover {
  background-color: #f8f9fa;
}

.settings-management .option-input {
  flex: 1;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.settings-management .option-input:focus {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
  outline: none;
}

/* 备份表格样式 */
.settings-management .backup-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  background: #fff;
}

.settings-management .backup-table th {
  background-color: #fafafa;
  padding: 16px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #333;
}

.settings-management .backup-table td {
  padding: 14px 12px;
  border-bottom: 1px solid #f5f5f5;
}

.settings-management .backup-table tr:hover {
  background-color: #f8f9fa;
}

.settings-management .backup-table tr:last-child td {
  border-bottom: none;
}

/* 按钮样式优化 */
.settings-management .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.settings-management .ant-btn-primary {
  background: #1677ff;
  border: none;
  box-shadow: 0 2px 6px rgba(22, 119, 255, 0.2);
}

.settings-management .ant-btn-primary:hover {
  background: #4096ff;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
  transform: translateY(-1px);
}

.settings-management .ant-btn-default {
  border-color: #d9d9d9;
  color: #666;
}

.settings-management .ant-btn-default:hover {
  border-color: #1677ff;
  color: #1677ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .settings-management {
    padding: 0;
  }

  .settings-management-card {
    margin-top: -8px;
  }

  .settings-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .settings-title {
    text-align: left;
    flex: none;
  }

  .settings-management .header-tabs {
    margin-left: 0;
    justify-content: flex-start;
    width: 100%;
  }

  .settings-management .header-tabs .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 14px;
  }

  .settings-management .settings-vertical-tabs .ant-tabs-nav {
    width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .settings-management .settings-vertical-tabs {
    flex-direction: column;
  }
}

/* 开关组件样式 */
.settings-management .ant-switch {
  background-color: #d9d9d9;
}

.settings-management .ant-switch-checked {
  background-color: #1677ff;
}

.settings-management .ant-switch:hover:not(.ant-switch-disabled) {
  background-color: #bfbfbf;
}

.settings-management .ant-switch-checked:hover:not(.ant-switch-disabled) {
  background-color: #4096ff;
}

/* 表单卡片样式 */
.settings-management .settings-form-card {
  background: #fff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.settings-management .settings-form-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.settings-management .form-card-title {
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

/* 输入框和选择器样式 */
.settings-management .ant-input,
.settings-management .ant-select-selector {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.settings-management .ant-input:focus,
.settings-management .ant-select-focused .ant-select-selector {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.settings-management .ant-input:hover,
.settings-management .ant-select:hover .ant-select-selector {
  border-color: #4096ff;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 上传组件样式 */
.settings-management .ant-upload-list-picture-card .ant-upload-list-item {
  border-radius: 6px;
}

.settings-management .ant-upload-select-picture-card {
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
  transition: all 0.3s ease;
}

.settings-management .ant-upload-select-picture-card:hover {
  border-color: #1677ff;
}