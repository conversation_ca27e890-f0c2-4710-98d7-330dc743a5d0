/* 卡片样式 */
.option-card {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e8edf3;
  overflow: hidden;
  margin-bottom: 16px;
}

/* 蓝色指示条样式 */
.blue-indicator {
  position: absolute;
  left: 0;
  top: 0;
  height: 30px;
  width: 4px;
  background-color: #1677FF;
  border-radius: 0;
}

/* 卡片标题样式 */
.card-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #18232c;
  padding: 12px 12px 12px 16px;
  position: relative;
}

/* 卡片内容区域 */
.card-content {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

/* 卡片底部区域 */
.card-footer {
  padding: 12px 16px;
  background-color: #f9fafb;
  border-top: 1px solid #f0f0f0;
} 