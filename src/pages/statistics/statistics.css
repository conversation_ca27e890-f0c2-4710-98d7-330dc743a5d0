/* 统计分析页面样式 */

.statistics-dashboard-container {
  padding: 16px 24px 24px;
  background-color: #fff;
}

/* 标签页样式 */
.statistics-tabs-container {
  background-color: white;
  margin-top: 20px;
}

.statistics-tabs {
  padding: 0 20px;
}

.statistics-tabs .ant-tabs-nav {
  margin-bottom: 20px;
}

.statistics-tabs .ant-tabs-tab {
  padding: 12px 16px;
  font-size: 15px;
}

.statistics-tabs .ant-tabs-content {
  padding-bottom: 20px;
}

.header-tabs {
  flex: 1;
  margin-left: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.header-tabs .ant-tabs {
  width: auto;
}

.header-tabs .ant-tabs-nav {
  margin-bottom: 0;
  width: auto;
  display: flex;
  justify-content: flex-end;
}

.header-tabs .ant-tabs-nav-wrap {
  display: flex;
  justify-content: flex-end;
}

.header-tabs .ant-tabs-nav-list {
  display: flex;
  justify-content: flex-end;
}

.header-tabs .ant-tabs-tab {
  padding: 8px 16px;
  font-size: 15px;
}

.statistics-tabs-content {
  margin-top: 20px;
}

/* 财务分析样式 */
.finance-stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.finance-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.finance-stat-title {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 10px;
}

.finance-stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.finance-stat-trend {
  display: flex;
  align-items: center;
  font-size: 13px;
  justify-content: space-between;
}

/* 学员来源表格样式 */
.source-table-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  height: 100%;
}

.source-table-header {
  display: flex;
  background-color: #f8f9fa;
  padding: 12px 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
}

.source-table-body {
  padding: 8px 0;
}

.source-table-row {
  display: flex;
  padding: 10px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.source-table-row:last-child {
  border-bottom: none;
}

.source-header-item,
.source-row-item {
  flex: 1;
  text-align: center;
}

.source-row-item:first-child {
  text-align: left;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  border-bottom: none;
}

.header-actions {
  display: flex;
  align-items: center;
}

.content-panel {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 0;
  border-bottom: none;
}

.btn-group {
  display: flex;
  gap: 8px;
}

.statistics-dashboard-container .time-filter-buttons {
  display: flex;
}

.statistics-dashboard-container .chart-content {
  padding: 0 20px 20px;
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statistics-tabs {
  margin-top: 24px;
}

.statistics-dashboard-container .chart-wrapper {
  position: relative;
}

.statistics-dashboard-container .data-table {
  width: 100%;
  border-collapse: collapse;
}

.statistics-dashboard-container .data-table th,
.statistics-dashboard-container .data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.statistics-dashboard-container .data-table th {
  background-color: #fafafa;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .chart-container {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .chart-container {
    padding: 10px;
  }
  .filter-section {
    flex-direction: column;
    gap: 12px;
  }
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
  .header-actions {
    margin-top: 10px;
  }
}