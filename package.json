{"name": "lesson_web_front", "version": "0.1.0", "private": true, "description": "课时管理系统前端", "dependencies": {"@ant-design/icons": "^5.3.0", "@reduxjs/toolkit": "^2.0.1", "@types/js-cookie": "^3.0.6", "@types/uuid": "^10.0.0", "antd": "^5.13.2", "axios": "^1.6.5", "crypto-browserify": "^3.12.1", "dayjs": "^1.11.10", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "js-cookie": "^3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^6.21.3", "typescript": "^5.3.3", "uuid": "^11.1.0"}, "devDependencies": {"@stagewise/toolbar-react": "^0.1.2", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.11", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.4", "vite": "^6.2.6"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}